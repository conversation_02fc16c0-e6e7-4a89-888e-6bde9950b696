{"version": 3, "file": "Connection.js", "sourceRoot": "", "sources": ["../../../../../src/common/bidi/Connection.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;AAEH,0CAAkC;AAClC,MAAM,iBAAiB,GAAG,IAAA,gBAAK,EAAC,gCAAgC,CAAC,CAAC;AAClE,MAAM,oBAAoB,GAAG,IAAA,gBAAK,EAAC,gCAAgC,CAAC,CAAC;AAGrE,wDAAgD;AAChD,4CAA2C;AA0B3C;;GAEG;AACH,MAAa,UAAW,SAAQ,8BAAY;IAO1C,YAAY,SAA8B,EAAE,KAAK,GAAG,CAAC;QACnD,KAAK,EAAE,CAAC;;QAPV,wCAAgC;QAChC,oCAAe;QACf,6BAAU,CAAC,EAAC;QACZ,6BAAU,KAAK,EAAC;QAChB,gCAA8C,IAAI,GAAG,EAAE,EAAC;QAItD,uBAAA,IAAI,qBAAU,KAAK,MAAA,CAAC;QAEpB,uBAAA,IAAI,yBAAc,SAAS,MAAA,CAAC;QAC5B,uBAAA,IAAI,6BAAW,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtD,uBAAA,IAAI,6BAAW,CAAC,OAAO,GAAG,uBAAA,IAAI,kDAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACrD,CAAC;IAED,IAAI,MAAM;QACR,OAAO,uBAAA,IAAI,0BAAQ,CAAC;IACtB,CAAC;IAED,IAAI,CAAC,MAAc,EAAE,MAAc;;QACjC,MAAM,EAAE,GAAG,iDAAA,CAAE,0DAAY,EAAd,IAAc,CAAA,MAAA,CAAC;QAC1B,MAAM,kBAAkB,GAAG,IAAI,CAAC,SAAS,CAAC;YACxC,EAAE;YACF,MAAM;YACN,MAAM;SACI,CAAC,CAAC;QACd,iBAAiB,CAAC,kBAAkB,CAAC,CAAC;QACtC,uBAAA,IAAI,6BAAW,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACzC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,uBAAA,IAAI,6BAAW,CAAC,GAAG,CAAC,EAAE,EAAE;gBACtB,OAAO;gBACP,MAAM;gBACN,KAAK,EAAE,IAAI,yBAAa,EAAE;gBAC1B,MAAM;aACP,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,SAAS,CAAC,OAAe;QACvC,IAAI,uBAAA,IAAI,yBAAO,EAAE;YACf,MAAM,IAAI,OAAO,CAAC,CAAC,CAAC,EAAE;gBACpB,OAAO,UAAU,CAAC,CAAC,EAAE,uBAAA,IAAI,yBAAO,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC;SACJ;QACD,oBAAoB,CAAC,OAAO,CAAC,CAAC;QAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAGd,CAAC;QACpB,IAAI,IAAI,IAAI,MAAM,EAAE;YAClB,MAAM,QAAQ,GAAG,uBAAA,IAAI,6BAAW,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAChD,sEAAsE;YACtE,IAAI,QAAQ,EAAE;gBACZ,uBAAA,IAAI,6BAAW,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBAClC,IAAI,OAAO,IAAI,MAAM,EAAE;oBACrB,QAAQ,CAAC,MAAM,CACb,mBAAmB,CAAC,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC,CAC7D,CAAC;iBACH;qBAAM;oBACL,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;iBACjC;aACF;SACF;aAAM;YACL,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;SACzC;IACH,CAAC;IAoBD,OAAO;QACL,uBAAA,IAAI,kDAAS,MAAb,IAAI,CAAW,CAAC;QAChB,uBAAA,IAAI,6BAAW,CAAC,KAAK,EAAE,CAAC;IAC1B,CAAC;CACF;AA7FD,gCA6FC;;IArBG,IAAI,uBAAA,IAAI,0BAAQ,EAAE;QAChB,OAAO;KACR;IACD,uBAAA,IAAI,sBAAW,IAAI,MAAA,CAAC;IACpB,uBAAA,IAAI,6BAAW,CAAC,SAAS,GAAG,SAAS,CAAC;IACtC,uBAAA,IAAI,6BAAW,CAAC,OAAO,GAAG,SAAS,CAAC;IACpC,KAAK,MAAM,QAAQ,IAAI,uBAAA,IAAI,6BAAW,CAAC,MAAM,EAAE,EAAE;QAC/C,QAAQ,CAAC,MAAM,CACb,YAAY,CACV,QAAQ,CAAC,KAAK,EACd,mBAAmB,QAAQ,CAAC,MAAM,uBAAuB,CAC1D,CACF,CAAC;KACH;IACD,uBAAA,IAAI,6BAAW,CAAC,KAAK,EAAE,CAAC;AAC1B,CAAC;AAQH,SAAS,YAAY,CACnB,KAAoB,EACpB,OAAe,EACf,eAAwB;IAExB,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;IACxB,KAAK,CAAC,eAAe,GAAG,eAAe,aAAf,eAAe,cAAf,eAAe,GAAI,KAAK,CAAC,eAAe,CAAC;IACjE,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,mBAAmB,CAC1B,KAAoB,EACpB,MAAc,EACd,MAAqB;IAErB,IAAI,OAAO,GAAG,mBAAmB,MAAM,MAAM,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;IAC9E,IAAI,MAAM,CAAC,UAAU,EAAE;QACrB,OAAO,IAAI,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;KACpC;IACD,OAAO,YAAY,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;AACtD,CAAC"}