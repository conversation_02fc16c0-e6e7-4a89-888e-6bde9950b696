{"version": 3, "file": "HTTPResponse.js", "sourceRoot": "", "sources": ["../../../../src/common/HTTPResponse.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAoBA,OAAO,EAAC,eAAe,EAAC,MAAM,sBAAsB,CAAC;AAErD,OAAO,EAAC,aAAa,EAAC,MAAM,aAAa,CAAC;AAiB1C;;;;;GAKG;AACH,MAAM,OAAO,YAAY;IAgBvB;;OAEG;IACH,YACE,MAAkB,EAClB,OAAoB,EACpB,eAA0C,EAC1C,SAAiE;;QAtBnE,uCAAoB;QACpB,wCAAsB;QACtB,uCAA0C,IAAI,EAAC;QAC/C,kDAA0C;QAC1C,iDAAyD,GAAG,EAAE,GAAE,CAAC,EAAC;QAClE,8CAA8B;QAC9B,uCAAgB;QAChB,2CAAoB;QACpB,oCAAa;QACb,8CAAwB;QACxB,kDAA4B;QAC5B,gCAAmC,EAAE,EAAC;QACtC,gDAAyC;QACzC,uCAAgD;QAW9C,uBAAA,IAAI,wBAAW,MAAM,MAAA,CAAC;QACtB,uBAAA,IAAI,yBAAY,OAAO,MAAA,CAAC;QAExB,uBAAA,IAAI,mCAAsB,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;YAC9C,uBAAA,IAAI,0CAA6B,OAAO,MAAA,CAAC;QAC3C,CAAC,CAAC,MAAA,CAAC;QAEH,uBAAA,IAAI,+BAAkB;YACpB,EAAE,EAAE,eAAe,CAAC,eAAe;YACnC,IAAI,EAAE,eAAe,CAAC,UAAU;SACjC,MAAA,CAAC;QACF,uBAAA,IAAI,4BACF,uBAAA,IAAI,0EAA6B,MAAjC,IAAI,EAA8B,SAAS,CAAC;YAC5C,eAAe,CAAC,UAAU,MAAA,CAAC;QAC7B,uBAAA,IAAI,qBAAQ,OAAO,CAAC,GAAG,EAAE,MAAA,CAAC;QAC1B,uBAAA,IAAI,+BAAkB,CAAC,CAAC,eAAe,CAAC,aAAa,MAAA,CAAC;QACtD,uBAAA,IAAI,mCAAsB,CAAC,CAAC,eAAe,CAAC,iBAAiB,MAAA,CAAC;QAE9D,uBAAA,IAAI,wBAAW,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,eAAe,CAAC,MAAM,MAAA,CAAC;QACzE,MAAM,OAAO,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC;QACxE,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YAClD,uBAAA,IAAI,6BAAS,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,CAAC;SAC1C;QAED,uBAAA,IAAI,iCAAoB,eAAe,CAAC,eAAe;YACrD,CAAC,CAAC,IAAI,eAAe,CAAC,eAAe,CAAC,eAAe,CAAC;YACtD,CAAC,CAAC,IAAI,MAAA,CAAC;QACT,uBAAA,IAAI,wBAAW,eAAe,CAAC,MAAM,IAAI,IAAI,MAAA,CAAC;IAChD,CAAC;IAuBD;;OAEG;IACH,YAAY,CAAC,GAAiB;QAC5B,IAAI,GAAG,EAAE;YACP,OAAO,uBAAA,IAAI,8CAA0B,MAA9B,IAAI,EAA2B,GAAG,CAAC,CAAC;SAC5C;QACD,OAAO,uBAAA,IAAI,8CAA0B,MAA9B,IAAI,CAA4B,CAAC;IAC1C,CAAC;IAED;;;OAGG;IACH,aAAa;QACX,OAAO,uBAAA,IAAI,mCAAe,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,GAAG;QACD,OAAO,uBAAA,IAAI,yBAAK,CAAC;IACnB,CAAC;IAED;;OAEG;IACH,EAAE;QACA,6BAA6B;QAC7B,OAAO,uBAAA,IAAI,4BAAQ,KAAK,CAAC,IAAI,CAAC,uBAAA,IAAI,4BAAQ,IAAI,GAAG,IAAI,uBAAA,IAAI,4BAAQ,IAAI,GAAG,CAAC,CAAC;IAC5E,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,OAAO,uBAAA,IAAI,4BAAQ,CAAC;IACtB,CAAC;IAED;;;OAGG;IACH,UAAU;QACR,OAAO,uBAAA,IAAI,gCAAY,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACH,OAAO;QACL,OAAO,uBAAA,IAAI,6BAAS,CAAC;IACvB,CAAC;IAED;;;OAGG;IACH,eAAe;QACb,OAAO,uBAAA,IAAI,qCAAiB,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,OAAO,uBAAA,IAAI,4BAAQ,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,IAAI,CAAC,uBAAA,IAAI,oCAAgB,EAAE;YACzB,uBAAA,IAAI,gCAAmB,uBAAA,IAAI,uCAAmB,CAAC,IAAI,CAAC,KAAK,EAAC,KAAK,EAAC,EAAE;gBAChE,IAAI,KAAK,EAAE;oBACT,MAAM,KAAK,CAAC;iBACb;gBACD,IAAI;oBACF,MAAM,QAAQ,GAAG,MAAM,uBAAA,IAAI,4BAAQ,CAAC,IAAI,CAAC,yBAAyB,EAAE;wBAClE,SAAS,EAAE,uBAAA,IAAI,6BAAS,CAAC,UAAU;qBACpC,CAAC,CAAC;oBACH,OAAO,MAAM,CAAC,IAAI,CAChB,QAAQ,CAAC,IAAI,EACb,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAC3C,CAAC;iBACH;gBAAC,OAAO,KAAK,EAAE;oBACd,IACE,KAAK,YAAY,aAAa;wBAC9B,KAAK,CAAC,eAAe,KAAK,yCAAyC,EACnE;wBACA,MAAM,IAAI,aAAa,CACrB,gGAAgG,CACjG,CAAC;qBACH;oBAED,MAAM,KAAK,CAAC;iBACb;YACH,CAAC,CAAC,MAAA,CAAC;SACJ;QACD,OAAO,uBAAA,IAAI,oCAAgB,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAI;QACR,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;QACpC,OAAO,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IAClC,CAAC;IAED;;;;;;;;OAQG;IACH,KAAK,CAAC,IAAI;QACR,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QAClC,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,OAAO;QACL,OAAO,uBAAA,IAAI,6BAAS,CAAC;IACvB,CAAC;IAED;;;OAGG;IACH,SAAS;QACP,OAAO,uBAAA,IAAI,mCAAe,IAAI,uBAAA,IAAI,6BAAS,CAAC,gBAAgB,CAAC;IAC/D,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,OAAO,uBAAA,IAAI,uCAAmB,CAAC;IACjC,CAAC;IAED;;;OAGG;IACH,KAAK;QACH,OAAO,uBAAA,IAAI,6BAAS,CAAC,KAAK,EAAE,CAAC;IAC/B,CAAC;CACF;suBAhLG,SAAiE;IAEjE,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;QACxC,OAAO;KACR;IACD,MAAM,SAAS,GAAG,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1D,IAAI,CAAC,SAAS,EAAE;QACd,OAAO;KACR;IACD,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;IAClD,IAAI,CAAC,KAAK,EAAE;QACV,OAAO;KACR;IACD,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IAC5B,IAAI,CAAC,UAAU,EAAE;QACf,OAAO;KACR;IACD,OAAO,UAAU,CAAC;AACpB,CAAC"}