{"version": 3, "file": "FrameTree.js", "sourceRoot": "", "sources": ["../../../../src/common/FrameTree.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;AAEH,mEAGoC;AAGpC;;;;;;GAMG;AACH,MAAa,SAAS;IAAtB;QACE,4BAAU,IAAI,GAAG,EAAiB,EAAC;QACnC,2BAA2B;QAC3B,+BAAa,IAAI,GAAG,EAAkB,EAAC;QACvC,2BAA2B;QAC3B,8BAAY,IAAI,GAAG,EAAuB,EAAC;QAC3C,uCAAmB;QACnB,kCAAgB,IAAI,GAAG,EAAuC,EAAC;IA0EjE,CAAC;IAxEC,YAAY;QACV,OAAO,uBAAA,IAAI,4BAAW,CAAC;IACzB,CAAC;IAED,OAAO,CAAC,OAAe;QACrB,OAAO,uBAAA,IAAI,yBAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;IACnC,CAAC;IAED;;;OAGG;IACH,YAAY,CAAC,OAAe;QAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QACpC,IAAI,KAAK,EAAE;YACT,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;SAC/B;QACD,MAAM,QAAQ,GAAG,IAAA,0CAAqB,GAAS,CAAC;QAChD,MAAM,SAAS,GACb,uBAAA,IAAI,+BAAc,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI,GAAG,EAA0B,CAAC;QACvE,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACxB,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,MAAM;QACJ,OAAO,KAAK,CAAC,IAAI,CAAC,uBAAA,IAAI,yBAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;IAC3C,CAAC;IAED,QAAQ,CAAC,KAAY;;QACnB,uBAAA,IAAI,yBAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACnC,IAAI,KAAK,CAAC,SAAS,EAAE;YACnB,uBAAA,IAAI,4BAAW,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC;YAChD,IAAI,CAAC,uBAAA,IAAI,2BAAU,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;gBACxC,uBAAA,IAAI,2BAAU,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;aAChD;YACD,uBAAA,IAAI,2BAAU,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,CAAE,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;SACrD;aAAM;YACL,uBAAA,IAAI,wBAAc,KAAK,MAAA,CAAC;SACzB;QACD,MAAA,uBAAA,IAAI,+BAAc,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,0CAAE,OAAO,CAAC,OAAO,CAAC,EAAE;YACnD,OAAO,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;IACL,CAAC;IAED,WAAW,CAAC,KAAY;;QACtB,uBAAA,IAAI,yBAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC/B,uBAAA,IAAI,4BAAW,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAClC,IAAI,KAAK,CAAC,SAAS,EAAE;YACnB,MAAA,uBAAA,IAAI,2BAAU,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,0CAAE,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;SACxD;aAAM;YACL,uBAAA,IAAI,wBAAc,SAAS,MAAA,CAAC;SAC7B;IACH,CAAC;IAED,WAAW,CAAC,OAAe;QACzB,MAAM,QAAQ,GAAG,uBAAA,IAAI,2BAAU,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC7C,IAAI,CAAC,QAAQ,EAAE;YACb,OAAO,EAAE,CAAC;SACX;QACD,OAAO,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC;aACxB,GAAG,CAAC,EAAE,CAAC,EAAE;YACR,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC1B,CAAC,CAAC;aACD,MAAM,CAAC,CAAC,KAAK,EAAkB,EAAE;YAChC,OAAO,KAAK,KAAK,SAAS,CAAC;QAC7B,CAAC,CAAC,CAAC;IACP,CAAC;IAED,WAAW,CAAC,OAAe;QACzB,MAAM,QAAQ,GAAG,uBAAA,IAAI,4BAAW,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAC9C,OAAO,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IACvD,CAAC;CACF;AAjFD,8BAiFC"}