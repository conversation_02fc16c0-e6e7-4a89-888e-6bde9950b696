{"version": 3, "file": "XPathQuerySelector.js", "sourceRoot": "", "sources": ["../../../../src/injected/XPathQuerySelector.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH;;GAEG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAAG,CAChC,IAAU,EACV,QAAgB,EACH,EAAE;IACf,MAAM,GAAG,GAAG,IAAI,CAAC,aAAa,IAAI,QAAQ,CAAC;IAC3C,MAAM,MAAM,GAAG,GAAG,CAAC,QAAQ,CACzB,QAAQ,EACR,IAAI,EACJ,IAAI,EACJ,WAAW,CAAC,uBAAuB,CACpC,CAAC;IACF,OAAO,MAAM,CAAC,eAAe,CAAC;AAChC,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,qBAAqB,GAAG,CAAC,IAAU,EAAE,QAAgB,EAAU,EAAE;IAC5E,MAAM,GAAG,GAAG,IAAI,CAAC,aAAa,IAAI,QAAQ,CAAC;IAC3C,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAC3B,QAAQ,EACR,IAAI,EACJ,IAAI,EACJ,WAAW,CAAC,0BAA0B,CACvC,CAAC;IACF,MAAM,KAAK,GAAW,EAAE,CAAC;IACzB,IAAI,IAAI,CAAC;IACT,OAAO,CAAC,IAAI,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC,EAAE;QACtC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAClB;IACD,OAAO,KAAK,CAAC;AACf,CAAC,CAAC"}