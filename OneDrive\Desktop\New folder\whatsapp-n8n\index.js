const { Client, LocalAuth } = require("whatsapp-web.js");
const express = require("express");
const bodyParser = require("body-parser");
const qrcode = require("qrcode-terminal");
const app = express();
const port = 3000;

app.use(bodyParser.json());

const client = new Client({
  authStrategy: new LocalAuth(), // Saves session locally
});

client.on("qr", (qr) => {
  console.log("Scan this QR code with WhatsApp:");
  qrcode.generate(qr, { small: true });
});

client.on("ready", () => {
  console.log("WhatsApp client is ready!");
});

// Your girlfriend's number (digits only)
const GF_NUMBER = "94743716644";

// AI Girlfriend Response Generator
function generateAIResponse(message) {
  const responses = {
    // Greetings
    greetings: [
      "Good morning my love! Hope you have the most amazing day! 😘☀️",
      "Hey beautiful! I was just thinking about you! 💕",
      "Hi babe! You always make my day brighter! ✨",
      "Morning sunshine! Can't wait to hear about your day! 🌅",
    ],

    // Love/Miss you messages
    love: [
      "Aww, I missed you too my love! You're always in my thoughts 💕",
      "I love you so much baby! You mean everything to me ❤️",
      "Missing you like crazy! Can't wait to talk more 😘",
      "You're the best thing that ever happened to me! 💖",
    ],

    // Sad/Support messages
    support: [
      "Aww baby, I'm here for you. Want to talk about it? I love you so much ❤️",
      "I'm sorry you're feeling down. You're so strong and I believe in you 💪💕",
      "Whatever it is, we'll get through it together. I'm always here for you 🤗",
      "Sending you all my love and hugs. You're amazing! 🫂❤️",
    ],

    // Questions about day/activities
    daily: [
      "My day is so much better now that I'm talking to you! How was yours? 😊",
      "Just thinking about you as always! Missing your beautiful smile 💕",
      "Nothing much, just wishing I could spend more time with you! 🥰",
      "Every moment is better when I'm talking to you! Tell me about your day! ✨",
    ],

    // Default responses
    default: [
      "You always know how to make me smile! I love talking with you 😊💕",
      "That's so sweet of you to say! You're incredible ❤️",
      "I love how we can talk about anything! You're amazing 🥰",
      "You're so thoughtful! This is why I adore you so much 💖",
    ],
  };

  const msg = message.toLowerCase();

  // Check message content and return appropriate response
  if (
    msg.includes("good morning") ||
    msg.includes("morning") ||
    msg.includes("hi") ||
    msg.includes("hello") ||
    msg.includes("hey")
  ) {
    return responses.greetings[
      Math.floor(Math.random() * responses.greetings.length)
    ];
  }

  if (
    msg.includes("love you") ||
    msg.includes("miss you") ||
    msg.includes("missed you")
  ) {
    return responses.love[Math.floor(Math.random() * responses.love.length)];
  }

  if (
    msg.includes("sad") ||
    msg.includes("down") ||
    msg.includes("upset") ||
    msg.includes("😢") ||
    msg.includes("😭")
  ) {
    return responses.support[
      Math.floor(Math.random() * responses.support.length)
    ];
  }

  if (
    msg.includes("how was") ||
    msg.includes("what are you") ||
    msg.includes("doing") ||
    msg.includes("your day")
  ) {
    return responses.daily[Math.floor(Math.random() * responses.daily.length)];
  }

  // Default response
  return responses.default[
    Math.floor(Math.random() * responses.default.length)
  ];
}

// Handle messages from your GF with AI responses
client.on("message", async (msg) => {
  // Normalize number: keep digits only
  const normalizedFrom = msg.from.replace(/\D/g, ""); // remove non-digits

  // Check if message is from GF
  if (normalizedFrom.endsWith(GF_NUMBER)) {
    // Only respond to non-empty messages
    if (msg.body && msg.body.trim().length > 0) {
      console.log(`📱 Received from GF: ${msg.body}`);

      // Generate AI response
      const aiResponse = generateAIResponse(msg.body);
      console.log(`🤖 AI Response: ${aiResponse}`);

      // Send AI response back to her
      try {
        await client.sendMessage(msg.from, aiResponse);
        console.log(`✅ AI response sent successfully!`);
      } catch (error) {
        console.error(`❌ Error sending AI response:`, error);
      }
    }
  }
});

// Receive replies from n8n → send to WhatsApp
app.post("/reply", async (req, res) => {
  console.log("Received request from n8n:", JSON.stringify(req.body, null, 2));

  // Handle different data formats from n8n
  let to, message;

  if (req.body && typeof req.body === "object") {
    // Check if it's direct format: {to: "...", message: "..."}
    if (req.body.to && req.body.message) {
      to = req.body.to;
      message = req.body.message;
    }
    // Check if it's nested format from n8n HTTP Request node
    else if (req.body.body && req.body.body.to && req.body.body.message) {
      to = req.body.body.to;
      message = req.body.body.message;
    }
    // Check if it's array format from n8n
    else if (Array.isArray(req.body) && req.body[0]) {
      const firstItem = req.body[0];
      to = firstItem.to || firstItem.body?.to;
      message = firstItem.message || firstItem.body?.message;
    }
  }

  if (!to || !message) {
    console.error("Missing 'to' or 'message' in request body");
    return res.status(400).json({ error: "Missing 'to' or 'message' field" });
  }

  try {
    // Ensure proper WhatsApp ID format
    let normalizedTo = to.trim();

    // If it's just a phone number, add @s.whatsapp.net
    if (!normalizedTo.includes("@")) {
      normalizedTo = normalizedTo + "@s.whatsapp.net";
    } else {
      // Replace @c.us with @s.whatsapp.net if needed
      normalizedTo = normalizedTo.replace("@c.us", "@s.whatsapp.net");
    }

    console.log(`Attempting to send message to: ${normalizedTo}`);
    console.log(`Message content: ${message}`);

    // Check if client is ready
    if (!client.info) {
      console.error("WhatsApp client is not ready yet!");
      return res.status(503).json({ error: "WhatsApp client not ready" });
    }

    await client.sendMessage(normalizedTo, message.trim());
    console.log(`✅ Message successfully sent to ${normalizedTo}: ${message}`);
    res.json({ success: true, message: "Message sent successfully" });
  } catch (err) {
    console.error("❌ Error sending message:", err);
    res
      .status(500)
      .json({ error: "Failed to send message", details: err.message });
  }
});

// Start the Express server
app.listen(port, () => {
  console.log(`🚀 Server running on http://localhost:${port}`);
});

// Initialize WhatsApp client
client.initialize();
