const { Client, LocalAuth } = require("whatsapp-web.js");
const express = require("express");
const bodyParser = require("body-parser");
const qrcode = require("qrcode-terminal");
const app = express();
const port = 3000;

app.use(bodyParser.json());

const client = new Client({
  authStrategy: new LocalAuth(), // Saves session locally
});

client.on("qr", (qr) => {
  console.log("Scan this QR code with WhatsApp:");
  qrcode.generate(qr, { small: true });
});

client.on("ready", () => {
  console.log("WhatsApp client is ready!");
});

// Your girlfriend's number (digits only)
const GF_NUMBER = "94743716644";

// Forward only messages from your GF to n8n
client.on("message", async (msg) => {
  const fetch = (await import("node-fetch")).default;

  // Normalize number: keep digits only
  const normalizedFrom = msg.from.replace(/\D/g, ""); // remove non-digits

  // Check if message is from GF
  if (normalizedFrom.endsWith(GF_NUMBER)) {
    // Only send non-empty messages
    if (msg.body && msg.body.trim().length > 0) {
      await fetch("http://localhost:5678/webhook-test/whatsapp-in", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          from: normalizedFrom + "@s.whatsapp.net",
          body: msg.body.trim(),
        }),
      });
      console.log(`Message forwarded to n8n: ${msg.body}`);
    }
  }
});

// Receive replies from n8n → send to WhatsApp
app.post("/reply", async (req, res) => {
  console.log("Received request from n8n:", JSON.stringify(req.body, null, 2));

  // Handle different data formats from n8n
  let to, message;

  if (req.body && typeof req.body === "object") {
    // Check if it's direct format: {to: "...", message: "..."}
    if (req.body.to && req.body.message) {
      to = req.body.to;
      message = req.body.message;
    }
    // Check if it's nested format from n8n HTTP Request node
    else if (req.body.body && req.body.body.to && req.body.body.message) {
      to = req.body.body.to;
      message = req.body.body.message;
    }
    // Check if it's array format from n8n
    else if (Array.isArray(req.body) && req.body[0]) {
      const firstItem = req.body[0];
      to = firstItem.to || firstItem.body?.to;
      message = firstItem.message || firstItem.body?.message;
    }
  }

  if (!to || !message) {
    console.error("Missing 'to' or 'message' in request body");
    return res.status(400).json({ error: "Missing 'to' or 'message' field" });
  }

  try {
    // Ensure proper WhatsApp ID format
    let normalizedTo = to.trim();

    // If it's just a phone number, add @s.whatsapp.net
    if (!normalizedTo.includes("@")) {
      normalizedTo = normalizedTo + "@s.whatsapp.net";
    } else {
      // Replace @c.us with @s.whatsapp.net if needed
      normalizedTo = normalizedTo.replace("@c.us", "@s.whatsapp.net");
    }

    console.log(`Attempting to send message to: ${normalizedTo}`);
    console.log(`Message content: ${message}`);

    // Check if client is ready
    if (!client.info) {
      console.error("WhatsApp client is not ready yet!");
      return res.status(503).json({ error: "WhatsApp client not ready" });
    }

    await client.sendMessage(normalizedTo, message.trim());
    console.log(`✅ Message successfully sent to ${normalizedTo}: ${message}`);
    res.json({ success: true, message: "Message sent successfully" });
  } catch (err) {
    console.error("❌ Error sending message:", err);
    res
      .status(500)
      .json({ error: "Failed to send message", details: err.message });
  }
});

// Start the Express server
app.listen(port, () => {
  console.log(`🚀 Server running on http://localhost:${port}`);
});

// Initialize WhatsApp client
client.initialize();
