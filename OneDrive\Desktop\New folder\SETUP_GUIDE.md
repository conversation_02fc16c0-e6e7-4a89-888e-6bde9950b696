# WhatsApp AI Auto-Reply Setup Guide

## 🎯 What This Does
- Your girlfriend sends WhatsA<PERSON> message → Node.js forwards to n8n
- n8n sends message to OpenAI → Gets intelligent boyfriend-like reply
- n8n sends AI reply back to WhatsApp → Your girlfriend receives it

## 📋 Prerequisites
1. ✅ Node.js WhatsApp server running (already done!)
2. ✅ n8n installed and running
3. 🔑 OpenAI API key

## 🚀 Setup Steps

### Step 1: Get OpenAI API Key
1. Go to https://platform.openai.com/api-keys
2. Create new API key
3. Copy the key (starts with `sk-...`)

### Step 2: Import Workflow to n8n
1. Open n8n in browser: http://localhost:5678
2. Click "+" → "Import from file"
3. Select the file: `whatsapp-ai-workflow.json`
4. Click "Import"

### Step 3: Configure OpenAI Credentials
1. In the imported workflow, click on "Generate AI Reply" node
2. Click "Create New Credential" for OpenAI API
3. Enter your OpenAI API key
4. Save the credential

### Step 4: Activate Webhook
1. Click on "WhatsApp Message Received" node
2. Copy the webhook URL (should be like: http://localhost:5678/webhook/whatsapp-in)
3. Test the webhook by clicking "Listen for calls"

### Step 5: Update Node.js Server
Your Node.js server needs to send messages to the correct webhook URL.

Current webhook URL in your index.js:
```javascript
await fetch("http://localhost:5678/webhook-test/whatsapp-in", {
```

Should be changed to:
```javascript
await fetch("http://localhost:5678/webhook/whatsapp-in", {
```

### Step 6: Test the Complete Flow
1. Make sure Node.js server is running
2. Make sure n8n workflow is active
3. Send a WhatsApp message from your girlfriend's number
4. Check n8n execution log
5. Check if AI reply is sent back to WhatsApp

## 🔧 Customization Options

### Personality Adjustment
Edit the system prompt in "Generate AI Reply" node:
```
You are a loving, caring boyfriend responding to your girlfriend's WhatsApp messages. 
Be warm, affectionate, and personal. Keep responses natural and conversational...
```

### Response Length
Adjust `maxTokens` in OpenAI node (currently 150)

### AI Model
Change from `gpt-3.5-turbo` to `gpt-4` for better responses (costs more)

## 🐛 Troubleshooting

### Messages not reaching n8n
- Check Node.js console for webhook errors
- Verify webhook URL is correct
- Test webhook manually with Postman

### AI not generating replies
- Check OpenAI API key is valid
- Check OpenAI account has credits
- Look at n8n execution logs

### Replies not sent to WhatsApp
- Check Node.js server is running on port 3000
- Verify WhatsApp client shows "ready"
- Check Node.js console for send errors

## 📱 Expected Flow
```
Girlfriend sends WhatsApp → Node.js → n8n → OpenAI → n8n → Node.js → WhatsApp reply
```

## ⚠️ Important Notes
- Keep your OpenAI API key secret
- Monitor OpenAI usage to avoid unexpected costs
- Test thoroughly before leaving it running
- Consider adding rate limiting for safety
