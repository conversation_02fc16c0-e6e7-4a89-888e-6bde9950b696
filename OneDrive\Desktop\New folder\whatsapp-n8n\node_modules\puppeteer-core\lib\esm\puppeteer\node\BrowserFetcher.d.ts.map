{"version": 3, "file": "BrowserFetcher.d.ts", "sourceRoot": "", "sources": ["../../../../src/node/BrowserFetcher.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAqBH,OAAO,EAAC,OAAO,EAAC,MAAM,sBAAsB,CAAC;AAoC7C;;;;GAIG;AACH,oBAAY,QAAQ,GAAG,OAAO,GAAG,KAAK,GAAG,SAAS,GAAG,OAAO,GAAG,OAAO,CAAC;AAyEvE;;GAEG;AACH,MAAM,WAAW,qBAAqB;IACpC,QAAQ,CAAC,EAAE,QAAQ,CAAC;IACpB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,IAAI,CAAC,EAAE,MAAM,CAAC;CACf;AAED;;GAEG;AACH,MAAM,WAAW,0BAA0B;IACzC,UAAU,EAAE,MAAM,CAAC;IACnB,cAAc,EAAE,MAAM,CAAC;IACvB,GAAG,EAAE,MAAM,CAAC;IACZ,KAAK,EAAE,OAAO,CAAC;IACf,QAAQ,EAAE,MAAM,CAAC;IACjB,OAAO,EAAE,MAAM,CAAC;CACjB;AACD;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AAEH,qBAAa,cAAc;;IAMzB;;OAEG;gBACS,WAAW,EAAE,MAAM,EAAE,OAAO,GAAE,qBAA0B;IAoDpE;;;OAGG;IACH,QAAQ,IAAI,QAAQ;IAIpB;;;OAGG;IACH,OAAO,IAAI,OAAO;IAIlB;;OAEG;IACH,IAAI,IAAI,MAAM;IAId;;;;;;;OAOG;IACH,WAAW,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;IAuB/C;;;;;;;;;OASG;IACG,QAAQ,CACZ,QAAQ,EAAE,MAAM,EAChB,gBAAgB,GAAE,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,KAAK,IAAqB,GAChE,OAAO,CAAC,0BAA0B,GAAG,SAAS,CAAC;IAsClD;;;;;OAKG;IACG,cAAc,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;IAqBzC;;;;;;OAMG;IACG,MAAM,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAW7C;;;OAGG;IACH,YAAY,CAAC,QAAQ,EAAE,MAAM,GAAG,0BAA0B;CA2E3D"}