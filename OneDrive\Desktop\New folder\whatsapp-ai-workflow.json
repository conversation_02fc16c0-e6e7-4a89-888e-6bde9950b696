{"name": "WhatsApp AI Auto-Reply", "nodes": [{"parameters": {"httpMethod": "POST", "path": "whatsapp-in", "options": {}}, "id": "webhook-trigger", "name": "WhatsApp Message Received", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "webhookId": "whatsapp-webhook-001"}, {"parameters": {"resource": "chat", "operation": "create", "model": "gpt-3.5-turbo", "messages": {"values": [{"role": "system", "content": "You are a loving, caring boyfriend responding to your girlfriend's WhatsApp messages. Be warm, affectionate, and personal. Keep responses natural and conversational, like a real boyfriend would text. Use emojis occasionally but don't overdo it. Be supportive, romantic when appropriate, and show genuine interest in what she's saying."}, {"role": "user", "content": "={{ $json.body.body }}"}]}, "options": {"temperature": 0.7, "maxTokens": 150}}, "id": "openai-chat", "name": "Generate AI Reply", "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1, "position": [460, 300], "credentials": {"openAiApi": {"id": "openai-credentials", "name": "OpenAI API"}}}, {"parameters": {"url": "http://localhost:3000/reply", "options": {"headers": {"Content-Type": "application/json"}, "body": {"to": "={{ $('WhatsApp Message Received').first().json.body.from }}", "message": "={{ $json.choices[0].message.content }}"}, "bodyType": "json"}, "method": "POST"}, "id": "send-reply", "name": "Send Reply to WhatsApp", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [680, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "condition-1", "leftValue": "={{ $json.body.body }}", "rightValue": "", "operator": {"type": "string", "operation": "notEmpty"}}], "combinator": "and"}, "options": {}}, "id": "filter-empty", "name": "Filter Empty Messages", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [350, 300]}], "connections": {"WhatsApp Message Received": {"main": [[{"node": "Filter Empty Messages", "type": "main", "index": 0}]]}, "Filter Empty Messages": {"main": [[{"node": "Generate AI Reply", "type": "main", "index": 0}]]}, "Generate AI Reply": {"main": [[{"node": "Send Reply to WhatsApp", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "id": "whatsapp-ai-workflow", "tags": []}