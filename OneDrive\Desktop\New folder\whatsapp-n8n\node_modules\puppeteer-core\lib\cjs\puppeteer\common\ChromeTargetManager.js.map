{"version": 3, "file": "ChromeTargetManager.js", "sourceRoot": "", "sources": ["../../../../src/common/ChromeTargetManager.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;AAGH,iDAAyC;AACzC,mDAAuD;AACvD,uDAA+C;AAE/C,uCAAqC;AASrC;;;;;;GAMG;AACH,MAAa,mBAAoB,SAAQ,8BAAY;IAkDnD,YACE,UAAsB,EACtB,aAA4B,EAC5B,oBAA2C;QAE3C,KAAK,EAAE,CAAC;;QAtDV,kDAAwB;QACxB;;;;;;;;;WASG;QACH,2DACE,IAAI,GAAG,EAAE,EAAC;QACZ;;;WAGG;QACH,yDAAkD,IAAI,GAAG,EAAE,EAAC;QAC5D;;;WAGG;QACH,0DAAmD,IAAI,GAAG,EAAE,EAAC;QAC7D;;;WAGG;QACH,8CAAkB,IAAI,GAAG,EAAU,EAAC;QACpC,4DAAwD;QACxD,qDAA8B;QAE9B,kDACE,IAAI,OAAO,EAAE,EAAC;QAEhB,kEAGI,IAAI,OAAO,EAAE,EAAC;QAClB,oEAGI,IAAI,OAAO,EAAE,EAAC;QAElB,kDAAsB,GAAG,EAAE,GAAE,CAAC,EAAC;QAC/B,iDAAoC,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;YACxD,uBAAA,IAAI,2CAAuB,OAAO,MAAA,CAAC;QACrC,CAAC,CAAC,EAAC;QACH,iDAAkC,IAAI,GAAG,EAAE,EAAC;QA6B5C,2DAA+B,GAAG,EAAE;YAClC,KAAK,MAAM,CACT,QAAQ,EACR,UAAU,EACX,IAAI,uBAAA,IAAI,wDAA6B,CAAC,OAAO,EAAE,EAAE;gBAChD,IACE,CAAC,CAAC,uBAAA,IAAI,iDAAsB;oBAC1B,uBAAA,IAAI,iDAAsB,MAA1B,IAAI,EAAuB,UAAU,CAAC,CAAC;oBACzC,UAAU,CAAC,IAAI,KAAK,SAAS,EAC7B;oBACA,uBAAA,IAAI,8CAAmB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;iBACvC;aACF;QACH,CAAC,EAAC;QAmFF,iDAAqB,CAAC,OAAmB,EAAE,EAAE;YAC3C,uBAAA,IAAI,sFAA2B,MAA/B,IAAI,EAA4B,OAAO,CAAC,CAAC;YACzC,uBAAA,IAAI,+CAAoB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAC3C,CAAC,EAAC;QAEF,+CAAmB,KAAK,EAAE,KAAyC,EAAE,EAAE;YACrE,uBAAA,IAAI,wDAA6B,CAAC,GAAG,CACnC,KAAK,CAAC,UAAU,CAAC,QAAQ,EACzB,KAAK,CAAC,UAAU,CACjB,CAAC;YAEF,IAAI,CAAC,IAAI,uEAA8C,KAAK,CAAC,UAAU,CAAC,CAAC;YAEzE,uEAAuE;YACvE,uEAAuE;YACvE,QAAQ;YACR,IAAI,KAAK,CAAC,UAAU,CAAC,IAAI,KAAK,SAAS,IAAI,KAAK,CAAC,UAAU,CAAC,QAAQ,EAAE;gBACpE,IAAI,uBAAA,IAAI,sDAA2B,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;oBAClE,OAAO;iBACR;gBACD,MAAM,MAAM,GAAG,uBAAA,IAAI,0CAAe,MAAnB,IAAI,EAAgB,KAAK,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;gBAChE,uBAAA,IAAI,sDAA2B,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;aACxE;YAED,IAAI,KAAK,CAAC,UAAU,CAAC,IAAI,KAAK,eAAe,EAAE;gBAC7C,sEAAsE;gBACtE,uEAAuE;gBACvE,SAAS;gBACT,MAAM,uBAAA,IAAI,uCAAY,CAAC,cAAc,CAAC,KAAK,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;aAC/D;QACH,CAAC,EAAC;QAEF,iDAAqB,CAAC,KAA2C,EAAE,EAAE;YACnE,MAAM,UAAU,GAAG,uBAAA,IAAI,wDAA6B,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YACzE,uBAAA,IAAI,wDAA6B,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;YACzD,uBAAA,IAAI,wFAA6B,MAAjC,IAAI,EAA8B,KAAK,CAAC,QAAQ,CAAC,CAAC;YAClD,IACE,CAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,IAAI,MAAK,gBAAgB;gBACrC,uBAAA,IAAI,sDAA2B,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,EACnD;gBACA,iEAAiE;gBACjE,2BAA2B;gBAC3B,MAAM,MAAM,GAAG,uBAAA,IAAI,sDAA2B,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;gBACnE,IAAI,CAAC,IAAI,2DAAwC,MAAM,CAAC,CAAC;gBACzD,uBAAA,IAAI,sDAA2B,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;aACxD;QACH,CAAC,EAAC;QAEF,mDAAuB,CAAC,KAA6C,EAAE,EAAE;YACvE,uBAAA,IAAI,wDAA6B,CAAC,GAAG,CACnC,KAAK,CAAC,UAAU,CAAC,QAAQ,EACzB,KAAK,CAAC,UAAU,CACjB,CAAC;YAEF,IACE,uBAAA,IAAI,2CAAgB,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC;gBACnD,CAAC,uBAAA,IAAI,sDAA2B,CAAC,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC;gBAC/D,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,EAC1B;gBACA,OAAO;aACR;YAED,MAAM,MAAM,GAAG,uBAAA,IAAI,sDAA2B,CAAC,GAAG,CAChD,KAAK,CAAC,UAAU,CAAC,QAAQ,CAC1B,CAAC;YACF,IAAI,CAAC,IAAI,iEAA2C;gBAClD,MAAM,EAAE,MAAO;gBACf,UAAU,EAAE,KAAK,CAAC,UAAU;aAC7B,CAAC,CAAC;QACL,CAAC,EAAC;QAEF,kDAAsB,KAAK,EACzB,aAAsC,EACtC,KAA4C,EAC5C,EAAE;YACF,MAAM,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;YACpC,MAAM,OAAO,GAAG,uBAAA,IAAI,uCAAY,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YAC1D,IAAI,CAAC,OAAO,EAAE;gBACZ,MAAM,IAAI,KAAK,CAAC,WAAW,KAAK,CAAC,SAAS,mBAAmB,CAAC,CAAC;aAChE;YAED,MAAM,YAAY,GAAG,KAAK,IAAI,EAAE;gBAC9B,MAAM,OAAO,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC,KAAK,CAAC,oBAAU,CAAC,CAAC;gBACxE,0EAA0E;gBAC1E,gDAAgD;gBAChD,MAAM,aAAa;qBAChB,IAAI,CAAC,yBAAyB,EAAE;oBAC/B,SAAS,EAAE,OAAO,CAAC,EAAE,EAAE;iBACxB,CAAC;qBACD,KAAK,CAAC,oBAAU,CAAC,CAAC;YACvB,CAAC,CAAC;YAEF,IAAI,CAAC,uBAAA,IAAI,uCAAY,CAAC,cAAc,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;gBACzD,OAAO;aACR;YAED,2EAA2E;YAC3E,wEAAwE;YACxE,sEAAsE;YACtE,6CAA6C;YAC7C,4EAA4E;YAC5E,wEAAwE;YACxE,OAAO;YACP,IACE,UAAU,CAAC,IAAI,KAAK,gBAAgB;gBACpC,uBAAA,IAAI,uCAAY,CAAC,cAAc,CAAC,UAAU,CAAC,QAAQ,CAAC,EACpD;gBACA,uBAAA,IAAI,wFAA6B,MAAjC,IAAI,EAA8B,UAAU,CAAC,QAAQ,CAAC,CAAC;gBACvD,MAAM,YAAY,EAAE,CAAC;gBACrB,IAAI,uBAAA,IAAI,sDAA2B,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;oBAC5D,OAAO;iBACR;gBACD,MAAM,MAAM,GAAG,uBAAA,IAAI,0CAAe,MAAnB,IAAI,EAAgB,UAAU,CAAC,CAAC;gBAC/C,uBAAA,IAAI,sDAA2B,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gBACjE,IAAI,CAAC,IAAI,qEAA6C,MAAM,CAAC,CAAC;gBAC9D,OAAO;aACR;YAED,IAAI,uBAAA,IAAI,iDAAsB,IAAI,CAAC,uBAAA,IAAI,iDAAsB,MAA1B,IAAI,EAAuB,UAAU,CAAC,EAAE;gBACzE,uBAAA,IAAI,2CAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;gBAC9C,uBAAA,IAAI,wFAA6B,MAAjC,IAAI,EAA8B,UAAU,CAAC,QAAQ,CAAC,CAAC;gBACvD,MAAM,YAAY,EAAE,CAAC;gBACrB,OAAO;aACR;YAED,MAAM,cAAc,GAAG,uBAAA,IAAI,sDAA2B,CAAC,GAAG,CACxD,UAAU,CAAC,QAAQ,CACpB,CAAC;YAEF,MAAM,MAAM,GAAG,cAAc;gBAC3B,CAAC,CAAC,uBAAA,IAAI,sDAA2B,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAE;gBAC3D,CAAC,CAAC,uBAAA,IAAI,0CAAe,MAAnB,IAAI,EAAgB,UAAU,EAAE,OAAO,CAAC,CAAC;YAE7C,uBAAA,IAAI,qFAA0B,MAA9B,IAAI,EAA2B,OAAO,CAAC,CAAC;YAExC,IAAI,cAAc,EAAE;gBAClB,uBAAA,IAAI,uDAA4B,CAAC,GAAG,CAClC,OAAO,CAAC,EAAE,EAAE,EACZ,uBAAA,IAAI,sDAA2B,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,CAAE,CAC1D,CAAC;aACH;iBAAM;gBACL,uBAAA,IAAI,sDAA2B,CAAC,GAAG,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gBACjE,uBAAA,IAAI,uDAA4B,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;aAC5D;YAED,KAAK,MAAM,WAAW,IAAI,uBAAA,IAAI,+CAAoB,CAAC,GAAG,CAAC,aAAa,CAAC;gBACnE,EAAE,EAAE;gBACJ,IAAI,CAAC,CAAC,aAAa,YAAY,0BAAU,CAAC,EAAE;oBAC1C,oEAAoE;oBACpE,0CAA0C;oBAC1C,IAAA,kBAAM,EAAC,uBAAA,IAAI,uDAA4B,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;iBAClE;gBACD,MAAM,WAAW,CACf,MAAM,EACN,aAAa,YAAY,0BAAU;oBACjC,CAAC,CAAC,IAAI;oBACN,CAAC,CAAC,uBAAA,IAAI,uDAA4B,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE,EAAE,CAAE,CAC9D,CAAC;aACH;YAED,uBAAA,IAAI,8CAAmB,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YACjD,IAAI,CAAC,cAAc,EAAE;gBACnB,IAAI,CAAC,IAAI,qEAA6C,MAAM,CAAC,CAAC;aAC/D;YACD,uBAAA,IAAI,wFAA6B,MAAjC,IAAI,CAA+B,CAAC;YAEpC,wEAAwE;YACxE,SAAS;YACT,MAAM,OAAO,CAAC,GAAG,CAAC;gBAChB,OAAO,CAAC,IAAI,CAAC,sBAAsB,EAAE;oBACnC,sBAAsB,EAAE,IAAI;oBAC5B,OAAO,EAAE,IAAI;oBACb,UAAU,EAAE,IAAI;iBACjB,CAAC;gBACF,OAAO,CAAC,IAAI,CAAC,iCAAiC,CAAC;aAChD,CAAC,CAAC,KAAK,CAAC,oBAAU,CAAC,CAAC;QACvB,CAAC,EAAC;QASF,oDAAwB,CACtB,cAAuC,EACvC,KAA8C,EAC9C,EAAE;YACF,MAAM,MAAM,GAAG,uBAAA,IAAI,uDAA4B,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YAErE,uBAAA,IAAI,uDAA4B,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YAEzD,IAAI,CAAC,MAAM,EAAE;gBACX,OAAO;aACR;YAED,uBAAA,IAAI,sDAA2B,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YACzD,IAAI,CAAC,IAAI,2DAAwC,MAAM,CAAC,CAAC;QAC3D,CAAC,EAAC;QA5TA,uBAAA,IAAI,mCAAe,UAAU,MAAA,CAAC;QAC9B,uBAAA,IAAI,6CAAyB,oBAAoB,MAAA,CAAC;QAClD,uBAAA,IAAI,sCAAkB,aAAa,MAAA,CAAC;QAEpC,uBAAA,IAAI,uCAAY,CAAC,EAAE,CAAC,sBAAsB,EAAE,uBAAA,IAAI,4CAAiB,CAAC,CAAC;QACnE,uBAAA,IAAI,uCAAY,CAAC,EAAE,CAAC,wBAAwB,EAAE,uBAAA,IAAI,8CAAmB,CAAC,CAAC;QACvE,uBAAA,IAAI,uCAAY,CAAC,EAAE,CAAC,0BAA0B,EAAE,uBAAA,IAAI,gDAAqB,CAAC,CAAC;QAC3E,uBAAA,IAAI,uCAAY,CAAC,EAAE,CAAC,iBAAiB,EAAE,uBAAA,IAAI,8CAAmB,CAAC,CAAC;QAChE,uBAAA,IAAI,qFAA0B,MAA9B,IAAI,EAA2B,uBAAA,IAAI,uCAAY,CAAC,CAAC;QAEjD,2EAA2E;QAC3E,sBAAsB;QACtB,uBAAA,IAAI,uCAAY;aACb,IAAI,CAAC,2BAA2B,EAAE;YACjC,QAAQ,EAAE,IAAI;YACd,MAAM,EAAE,CAAC,EAAC,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAC,EAAE,EAAE,CAAC;SACpC,CAAC;aACR,IAAI,CAAC,uBAAA,IAAI,wDAA6B,CAAC;aACvC,KAAK,CAAC,oBAAU,CAAC,CAAC;IACvB,CAAC;IAiBD,KAAK,CAAC,UAAU;QACd,MAAM,uBAAA,IAAI,uCAAY,CAAC,IAAI,CAAC,sBAAsB,EAAE;YAClD,sBAAsB,EAAE,IAAI;YAC5B,OAAO,EAAE,IAAI;YACb,UAAU,EAAE,IAAI;SACjB,CAAC,CAAC;QACH,uBAAA,IAAI,wFAA6B,MAAjC,IAAI,CAA+B,CAAC;QACpC,MAAM,uBAAA,IAAI,8CAAmB,CAAC;IAChC,CAAC;IAED,OAAO;QACL,uBAAA,IAAI,uCAAY,CAAC,GAAG,CAAC,sBAAsB,EAAE,uBAAA,IAAI,4CAAiB,CAAC,CAAC;QACpE,uBAAA,IAAI,uCAAY,CAAC,GAAG,CAAC,wBAAwB,EAAE,uBAAA,IAAI,8CAAmB,CAAC,CAAC;QACxE,uBAAA,IAAI,uCAAY,CAAC,GAAG,CAAC,0BAA0B,EAAE,uBAAA,IAAI,gDAAqB,CAAC,CAAC;QAC5E,uBAAA,IAAI,uCAAY,CAAC,GAAG,CAAC,iBAAiB,EAAE,uBAAA,IAAI,8CAAmB,CAAC,CAAC;QAEjE,uBAAA,IAAI,sFAA2B,MAA/B,IAAI,EAA4B,uBAAA,IAAI,uCAAY,CAAC,CAAC;IACpD,CAAC;IAED,mBAAmB;QACjB,OAAO,uBAAA,IAAI,sDAA2B,CAAC;IACzC,CAAC;IAED,oBAAoB,CAClB,OAAgC,EAChC,WAA8B;QAE9B,MAAM,YAAY,GAAG,uBAAA,IAAI,+CAAoB,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QACjE,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC/B,uBAAA,IAAI,+CAAoB,CAAC,GAAG,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;IACtD,CAAC;IAED,uBAAuB,CACrB,MAA+B,EAC/B,WAA8B;QAE9B,MAAM,YAAY,GAAG,uBAAA,IAAI,+CAAoB,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;QAChE,uBAAA,IAAI,+CAAoB,CAAC,GAAG,CAC1B,MAAM,EACN,YAAY,CAAC,MAAM,CAAC,kBAAkB,CAAC,EAAE;YACvC,OAAO,kBAAkB,KAAK,WAAW,CAAC;QAC5C,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;CA8OF;AArXD,kDAqXC;2zCA5O2B,OAAgC;IACxD,MAAM,QAAQ,GAAG,CAAC,KAA4C,EAAE,EAAE;QAChE,OAAO,uBAAA,IAAI,+CAAoB,MAAxB,IAAI,EAAqB,OAAO,EAAE,KAAK,CAAC,CAAC;IAClD,CAAC,CAAC;IACF,IAAA,kBAAM,EAAC,CAAC,uBAAA,IAAI,+DAAoC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;IAC/D,uBAAA,IAAI,+DAAoC,CAAC,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IAChE,OAAO,CAAC,EAAE,CAAC,yBAAyB,EAAE,QAAQ,CAAC,CAAC;IAEhD,MAAM,gBAAgB,GAAG,CACvB,KAA8C,EAC9C,EAAE;QACF,OAAO,uBAAA,IAAI,iDAAsB,MAA1B,IAAI,EAAuB,OAAO,EAAE,KAAK,CAAC,CAAC;IACpD,CAAC,CAAC;IACF,IAAA,kBAAM,EAAC,CAAC,uBAAA,IAAI,iEAAsC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;IACjE,uBAAA,IAAI,iEAAsC,CAAC,GAAG,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;IAC1E,OAAO,CAAC,EAAE,CAAC,2BAA2B,EAAE,gBAAgB,CAAC,CAAC;AAC5D,CAAC,2GAE0B,OAAgC;IACzD,IAAI,uBAAA,IAAI,+DAAoC,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;QACzD,OAAO,CAAC,GAAG,CACT,yBAAyB,EACzB,uBAAA,IAAI,+DAAoC,CAAC,GAAG,CAAC,OAAO,CAAE,CACvD,CAAC;QACF,uBAAA,IAAI,+DAAoC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;KAC1D;IAED,IAAI,uBAAA,IAAI,iEAAsC,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;QAC3D,OAAO,CAAC,GAAG,CACT,2BAA2B,EAC3B,uBAAA,IAAI,iEAAsC,CAAC,GAAG,CAAC,OAAO,CAAE,CACzD,CAAC;QACF,uBAAA,IAAI,iEAAsC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;KAC5D;AACH,CAAC,+GAoL4B,QAAiB;IAC5C,QAAQ,KAAK,SAAS,IAAI,uBAAA,IAAI,8CAAmB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IACnE,IAAI,uBAAA,IAAI,8CAAmB,CAAC,IAAI,KAAK,CAAC,EAAE;QACtC,uBAAA,IAAI,+CAAoB,MAAxB,IAAI,CAAsB,CAAC;KAC5B;AACH,CAAC"}