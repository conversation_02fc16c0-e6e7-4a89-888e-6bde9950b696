{"version": 3, "file": "TextQuerySelector.js", "sourceRoot": "", "sources": ["../../../../src/injected/TextQuerySelector.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,qDAG0B;AAE1B;;;;GAIG;AACI,MAAM,iBAAiB,GAAG,CAC/B,IAAU,EACV,QAAgB,EACA,EAAE;IAClB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE;QAClC,IAAI,IAAI,YAAY,OAAO,IAAI,IAAA,8CAA6B,EAAC,IAAI,CAAC,EAAE;YAClE,IAAI,WAA2B,CAAC;YAChC,IAAI,IAAI,CAAC,UAAU,EAAE;gBACnB,WAAW,GAAG,IAAA,yBAAiB,EAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;aAC5D;iBAAM;gBACL,WAAW,GAAG,IAAA,yBAAiB,EAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;aACjD;YACD,IAAI,WAAW,EAAE;gBACf,OAAO,WAAW,CAAC;aACpB;SACF;KACF;IAED,IAAI,IAAI,YAAY,OAAO,EAAE;QAC3B,MAAM,WAAW,GAAG,IAAA,kCAAiB,EAAC,IAAI,CAAC,CAAC;QAC5C,IAAI,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;YACvC,OAAO,IAAI,CAAC;SACb;KACF;IACD,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAzBW,QAAA,iBAAiB,qBAyB5B;AAEF;;;;GAIG;AACI,MAAM,oBAAoB,GAAG,CAClC,IAAU,EACV,QAAgB,EACL,EAAE;IACb,IAAI,OAAO,GAAc,EAAE,CAAC;IAC5B,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE;QAClC,IAAI,IAAI,YAAY,OAAO,EAAE;YAC3B,IAAI,YAAuB,CAAC;YAC5B,IAAI,IAAI,CAAC,UAAU,EAAE;gBACnB,YAAY,GAAG,IAAA,4BAAoB,EAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;aAChE;iBAAM;gBACL,YAAY,GAAG,IAAA,4BAAoB,EAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;aACrD;YACD,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;SACxC;KACF;IACD,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;QACtB,OAAO,OAAO,CAAC;KAChB;IAED,IAAI,IAAI,YAAY,OAAO,EAAE;QAC3B,MAAM,WAAW,GAAG,IAAA,kCAAiB,EAAC,IAAI,CAAC,CAAC;QAC5C,IAAI,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;YACvC,OAAO,CAAC,IAAI,CAAC,CAAC;SACf;KACF;IACD,OAAO,EAAE,CAAC;AACZ,CAAC,CAAC;AA3BW,QAAA,oBAAoB,wBA2B/B"}