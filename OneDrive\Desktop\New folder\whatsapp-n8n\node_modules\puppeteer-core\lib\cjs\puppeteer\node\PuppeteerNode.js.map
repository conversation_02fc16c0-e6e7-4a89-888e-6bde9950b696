{"version": 3, "file": "PuppeteerNode.js", "sourceRoot": "", "sources": ["../../../../src/node/PuppeteerNode.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;AAKH,yDAIgC;AAChC,kDAAoD;AACpD,2DAA0E;AAE1E,6DAAqE;AAarE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAkCG;AACH,MAAa,aAAc,SAAQ,wBAAS;IAU1C;;OAEG;IACH,YACE,QAI2B;QAE3B,MAAM,EAAC,WAAW,EAAE,iBAAiB,EAAE,WAAW,EAAE,GAAG,cAAc,EAAC,GACpE,QAAQ,CAAC;QACX,KAAK,CAAC,cAAc,CAAC,CAAC;QArBxB,0CAA4B;QAC5B,6CAAsB;QACtB,6CAAuB;QAEvB;;WAEG;QACH,uBAAkB,GAAG,kCAAmB,CAAC,QAAQ,CAAC;QAehD,uBAAA,IAAI,8BAAgB,WAAW,MAAA,CAAC;QAChC,uBAAA,IAAI,8BAAgB,WAAW,MAAA,CAAC;QAChC,IAAI,iBAAiB,EAAE;YACrB,IAAI,CAAC,kBAAkB,GAAG,iBAAiB,CAAC;SAC7C;QAED,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/C,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACnE,CAAC;IAED;;;;;OAKG;IACM,OAAO,CAAC,OAAuB;QACtC,OAAO,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAI,YAAY;QACd,OAAO,uBAAA,IAAI,kCAAa,CAAC;IAC3B,CAAC;IACD,IAAI,YAAY,CAAC,IAAyB;QACxC,IAAI,uBAAA,IAAI,kCAAa,KAAK,IAAI,EAAE;YAC9B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;SAC7B;QACD,uBAAA,IAAI,8BAAgB,IAAI,MAAA,CAAC;IAC3B,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA8BG;IACH,MAAM,CAAC,UAAkC,EAAE;QACzC,IAAI,OAAO,CAAC,OAAO,EAAE;YACnB,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC;SACrC;QACD,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACxC,CAAC;IAED;;;;;;;;;OASG;IACH,cAAc,CAAC,OAAgB;QAC7B,OAAO,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,IACE,CAAC,uBAAA,IAAI,+BAAU;YACf,uBAAA,IAAI,+BAAU,CAAC,OAAO,KAAK,IAAI,CAAC,YAAY;YAC5C,IAAI,CAAC,eAAe,EACpB;YACA,QAAQ,IAAI,CAAC,YAAY,EAAE;gBACzB,KAAK,SAAS;oBACZ,IAAI,CAAC,kBAAkB,GAAG,kCAAmB,CAAC,OAAO,CAAC;oBACtD,MAAM;gBACR,KAAK,QAAQ,CAAC;gBACd;oBACE,IAAI,CAAC,kBAAkB,GAAG,kCAAmB,CAAC,QAAQ,CAAC;aAC1D;YACD,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;YAC7B,uBAAA,IAAI,2BAAa,IAAA,mCAAc,EAC7B,uBAAA,IAAI,kCAAa,EACjB,IAAI,CAAC,kBAAkB,EACvB,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,YAAY,CAClB,MAAA,CAAC;SACH;QACD,OAAO,uBAAA,IAAI,+BAAU,CAAC;IACxB,CAAC;IAED;;;;;;;;OAQG;IACH,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;IAChC,CAAC;IAED;;;OAGG;IACH,WAAW,CAAC,UAAwC,EAAE;QACpD,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IAC7C,CAAC;IAED;;;;OAIG;IACH,oBAAoB,CAAC,OAA8B;QACjD,IAAI,CAAC,uBAAA,IAAI,kCAAa,EAAE;YACtB,MAAM,IAAI,KAAK,CACb,+DAA+D,CAChE,CAAC;SACH;QACD,OAAO,IAAI,kCAAc,CAAC,uBAAA,IAAI,kCAAa,EAAE,OAAO,CAAC,CAAC;IACxD,CAAC;CACF;AA7KD,sCA6KC"}