# 🧪 How to Test the Complete WhatsApp AI Flow

## Prerequisites Check ✅

Before testing, make sure these are running:

### 1. Node.js WhatsApp Server
```bash
# Should be running already, but if not:
cd whatsapp-n8n
node index.js
```
**Expected output:**
```
🚀 Server running on http://localhost:3000
WhatsApp client is ready!
```

### 2. n8n Server
```bash
# In a new terminal:
npx n8n
```
**Expected output:**
```
n8n ready on 0.0.0.0, port 5678
```

### 3. Import Workflow to n8n
1. Open http://localhost:5678 in browser
2. Click "+" → "Import from file"
3. Select `whatsapp-ai-workflow.json`
4. Click "Import"

### 4. Configure OpenAI API Key
1. In the workflow, click "Generate AI Reply" node
2. Click "Create New Credential" for OpenAI API
3. Enter your OpenAI API key (get from https://platform.openai.com/api-keys)
4. Save credential

### 5. Activate Workflow
1. Click the toggle switch to activate the workflow
2. Make sure it shows "Active"

## 🚀 Run the Complete Test

### Method 1: PowerShell Script (Recommended)
```powershell
# Run this in PowerShell:
.\test-complete-flow.ps1
```

### Method 2: Manual Test with PowerShell
```powershell
# Test the n8n webhook directly:
$testMessage = @{
    from = "<EMAIL>"
    body = "Hey babe, how was your day? I miss you! 💕"
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:5678/webhook/whatsapp-in" -Method POST -ContentType "application/json" -Body $testMessage
```

### Method 3: Test with curl (if available)
```bash
curl -X POST http://localhost:5678/webhook/whatsapp-in \
  -H "Content-Type: application/json" \
  -d '{"from":"<EMAIL>","body":"Hey babe, how was your day? I miss you! 💕"}'
```

## 📊 What Should Happen

### 1. Immediate Response
- PowerShell should show "✅ Message successfully sent to n8n workflow!"

### 2. n8n Processing (check at http://localhost:5678)
- New execution should appear in n8n
- Should show 4 nodes executed successfully:
  1. ✅ WhatsApp Message Received
  2. ✅ Filter Empty Messages  
  3. ✅ Generate AI Reply
  4. ✅ Send Reply to WhatsApp

### 3. Node.js Console Output
```
Received request from n8n: { to: '<EMAIL>', message: 'AI generated reply here' }
Attempting to send message to: <EMAIL>
Message content: AI generated reply here
✅ Message successfully <NAME_EMAIL>: AI generated reply here
```

### 4. WhatsApp
- You should receive an AI-generated reply on WhatsApp!

## 🐛 Troubleshooting

### Error: "Connection refused to localhost:5678"
- **Problem:** n8n is not running
- **Solution:** Start n8n with `npx n8n`

### Error: "Webhook not found"
- **Problem:** Workflow not imported or not active
- **Solution:** Import `whatsapp-ai-workflow.json` and activate it

### n8n shows error in "Generate AI Reply" node
- **Problem:** OpenAI API key not configured or invalid
- **Solution:** Add valid OpenAI API key to the node credentials

### n8n executes but no WhatsApp message received
- **Problem:** Node.js server not running or WhatsApp not ready
- **Solution:** Check Node.js console shows "WhatsApp client is ready!"

### AI reply is sent but not received on WhatsApp
- **Problem:** Phone number format or WhatsApp session issue
- **Solution:** Check Node.js logs for sending errors

## 🎯 Success Indicators

✅ PowerShell test completes without errors
✅ n8n shows successful execution
✅ Node.js shows message sent successfully  
✅ WhatsApp receives AI-generated reply

## 📱 Real Test with Girlfriend's Message

Once the test works, send a real WhatsApp message from your girlfriend's phone to trigger the actual flow!
