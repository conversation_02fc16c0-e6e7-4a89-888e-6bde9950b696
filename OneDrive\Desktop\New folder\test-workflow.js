// Test script to simulate WhatsApp message and test the complete workflow
const fetch = require('node-fetch');

async function testWorkflow() {
  console.log('🧪 Testing WhatsApp AI Workflow...\n');
  
  // Simulate a WhatsApp message from your girlfriend
  const testMessage = {
    from: "<EMAIL>",
    body: "Hey babe, how was your day? I miss you! 💕"
  };
  
  try {
    console.log('📱 Simulating WhatsApp message from girlfriend...');
    console.log('Message:', testMessage.body);
    console.log('From:', testMessage.from);
    
    // Send to n8n webhook (this simulates what your Node.js server does)
    const response = await fetch('http://localhost:5678/webhook/whatsapp-in', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(testMessage)
    });
    
    if (response.ok) {
      console.log('✅ Message successfully sent to n8n workflow');
      console.log('🤖 n8n should now:');
      console.log('   1. Process the message with OpenAI');
      console.log('   2. Generate a loving boyfriend reply');
      console.log('   3. Send reply back to WhatsApp via Node.js server');
      console.log('\n📱 Check your WhatsApp for the AI-generated reply!');
    } else {
      console.log('❌ Failed to send message to n8n');
      console.log('Status:', response.status);
      console.log('Make sure n8n is running and the workflow is active');
    }
    
  } catch (error) {
    console.log('❌ Error testing workflow:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Make sure n8n is running: http://localhost:5678');
    console.log('2. Import and activate the workflow');
    console.log('3. Configure OpenAI credentials');
    console.log('4. Make sure Node.js WhatsApp server is running');
  }
}

// Run the test
testWorkflow();
